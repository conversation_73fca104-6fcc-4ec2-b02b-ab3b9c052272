/**
 * 音频数据智能优化工具
 * 提供基于性能复杂度评估的自适应音频数据优化功能
 */

import { audioLogger } from "@/utils/logger/logger";

// 扩展的音频振幅数据接口
export interface OptimizedAudioAmplitudeData {
  samples: number[];
  sample_rate: number;
  duration_ms: number;
  max_amplitude: number;
  min_amplitude: number;
  // 优化相关元数据
  isOptimized?: boolean;
  originalSampleCount?: number;
  complexityScore?: number;
  complexityLevel?: 'low' | 'medium' | 'high' | 'extreme';
  optimizationRatio?: number; // 优化后的采样点比例
  preservedFeatures?: number; // 保留的关键特征点数量
}

// 性能复杂度评估结果
export interface PerformanceComplexity {
  score: number;
  level: 'low' | 'medium' | 'high' | 'extreme';
  needsOptimization: boolean;
  recommendedSampleCount: number;
  amplitudeRange: number;
  variationRate: number;
  description: string;
}

// 关键特征点信息
export interface KeyFeature {
  index: number;
  type: 'peak' | 'valley' | 'inflection' | 'boundary';
  amplitude: number;
  importance: number;
}

/**
 * 计算音频数据的性能复杂度
 */
export function calculatePerformanceComplexity(audioData: OptimizedAudioAmplitudeData): PerformanceComplexity {
  const sampleCount = audioData.samples.length;
  const amplitudeRange = Math.abs(audioData.max_amplitude) + Math.abs(audioData.min_amplitude);
  
  // 计算振幅变化率（相邻点的平均变化）
  const variationRate = calculateAmplitudeVariation(audioData.samples);
  
  // 基础复杂度：点数 × 振幅范围
  const baseComplexity = sampleCount * amplitudeRange;
  
  // 变化率权重（高频变化增加复杂度）
  const variationWeight = 1 + (variationRate * 0.5);
  
  // 最终复杂度分数
  const score = baseComplexity * variationWeight;
  
  // 分级判断和推荐
  let level: 'low' | 'medium' | 'high' | 'extreme';
  let needsOptimization = false;
  let recommendedSampleCount = sampleCount;
  let description = '';
  
  if (score < 3000) {
    level = 'low';
    description = '低复杂度，性能良好，无需优化';
  } else if (score < 8000) {
    level = 'medium';
    needsOptimization = true;
    recommendedSampleCount = Math.floor(sampleCount * 0.8); // 保留80%
    description = '中等复杂度，建议轻度优化';
  } else if (score < 15000) {
    level = 'high';
    needsOptimization = true;
    recommendedSampleCount = Math.floor(sampleCount * 0.6); // 保留60%
    description = '高复杂度，建议中度优化';
  } else {
    level = 'extreme';
    needsOptimization = true;
    recommendedSampleCount = Math.floor(sampleCount * 0.4); // 保留40%
    description = '极高复杂度，建议重度优化';
  }
  
  return {
    score,
    level,
    needsOptimization,
    recommendedSampleCount,
    amplitudeRange,
    variationRate,
    description
  };
}

/**
 * 计算振幅变化率
 */
function calculateAmplitudeVariation(samples: number[]): number {
  if (samples.length < 2) return 0;
  
  let totalVariation = 0;
  for (let i = 1; i < samples.length; i++) {
    totalVariation += Math.abs(samples[i] - samples[i - 1]);
  }
  
  return totalVariation / (samples.length - 1);
}

/**
 * 检测音频数据中的关键特征点
 */
export function detectKeyFeatures(samples: number[]): KeyFeature[] {
  const features: KeyFeature[] = [];
  
  // 添加边界点
  features.push({
    index: 0,
    type: 'boundary',
    amplitude: samples[0],
    importance: 1.0
  });
  
  features.push({
    index: samples.length - 1,
    type: 'boundary',
    amplitude: samples[samples.length - 1],
    importance: 1.0
  });
  
  // 检测内部特征点
  for (let i = 1; i < samples.length - 1; i++) {
    const prev = samples[i - 1];
    const curr = samples[i];
    const next = samples[i + 1];
    
    // 检测局部极值点（波峰、波谷）
    if (curr > prev && curr > next) {
      // 波峰
      features.push({
        index: i,
        type: 'peak',
        amplitude: curr,
        importance: Math.abs(curr) * 1.5 // 波峰重要性更高
      });
    } else if (curr < prev && curr < next) {
      // 波谷
      features.push({
        index: i,
        type: 'valley',
        amplitude: curr,
        importance: Math.abs(curr) * 1.5 // 波谷重要性更高
      });
    }
    
    // 检测显著变化点（斜率变化大的转折点）
    const slope1 = curr - prev;
    const slope2 = next - curr;
    const slopeChange = Math.abs(slope1 - slope2);
    
    if (slopeChange > 0.1) { // 阈值可调
      features.push({
        index: i,
        type: 'inflection',
        amplitude: curr,
        importance: Math.abs(curr) + slopeChange * 0.5
      });
    }
  }
  
  return features;
}

/**
 * 计算每个采样点的重要性分数
 */
export function calculateImportanceScores(samples: number[], keyFeatures: KeyFeature[]): number[] {
  const scores = new Array(samples.length).fill(0);
  
  // 基础分数：振幅大小
  for (let i = 0; i < samples.length; i++) {
    scores[i] = Math.abs(samples[i]);
  }
  
  // 关键特征点加分
  for (const feature of keyFeatures) {
    if (feature.index >= 0 && feature.index < scores.length) {
      scores[feature.index] += feature.importance;
    }
  }
  
  return scores;
}

/**
 * 选择最优的采样点，确保时间分布均匀且保留重要特征
 */
export function selectOptimalPoints(
  importanceScores: number[],
  targetCount: number,
  keyFeatures: KeyFeature[]
): number[] {
  const originalCount = importanceScores.length;
  
  if (originalCount <= targetCount) {
    return Array.from({ length: originalCount }, (_, i) => i);
  }
  
  // 强制保留的关键特征点
  const mustKeepIndices = new Set<number>();
  
  // 保留所有边界点和最重要的特征点
  for (const feature of keyFeatures) {
    if (feature.type === 'boundary' || feature.importance > 1.0) {
      mustKeepIndices.add(feature.index);
    }
  }
  
  // 创建索引-分数对，排除已强制保留的点
  const candidatePoints: { index: number; score: number }[] = [];
  for (let i = 0; i < importanceScores.length; i++) {
    if (!mustKeepIndices.has(i)) {
      candidatePoints.push({ index: i, score: importanceScores[i] });
    }
  }
  
  // 按重要性排序
  candidatePoints.sort((a, b) => b.score - a.score);
  
  // 计算还需要选择的点数
  const remainingCount = targetCount - mustKeepIndices.size;
  
  // 选择剩余的重要点
  const selectedCandidates = candidatePoints.slice(0, Math.max(0, remainingCount));
  
  // 合并所有选中的点
  const allSelectedIndices = [
    ...Array.from(mustKeepIndices),
    ...selectedCandidates.map(p => p.index)
  ];
  
  // 按时间顺序排序
  allSelectedIndices.sort((a, b) => a - b);
  
  // 确保时间分布相对均匀（可选优化）
  return ensureTimeDistribution(allSelectedIndices, originalCount, targetCount);
}

/**
 * 确保选中的点在时间轴上分布相对均匀
 */
function ensureTimeDistribution(
  selectedIndices: number[],
  originalCount: number,
  targetCount: number
): number[] {
  // 如果选中的点数已经合适，直接返回
  if (selectedIndices.length <= targetCount) {
    return selectedIndices;
  }
  
  // 计算理想的时间间隔
  const idealInterval = originalCount / targetCount;
  
  // 使用贪心算法选择分布最均匀的点
  const result: number[] = [];
  let lastSelectedIndex = -idealInterval;
  
  for (const index of selectedIndices) {
    if (index - lastSelectedIndex >= idealInterval * 0.8 || result.length === 0) {
      result.push(index);
      lastSelectedIndex = index;
      
      if (result.length >= targetCount) {
        break;
      }
    }
  }
  
  // 如果还没有达到目标数量，补充一些点
  if (result.length < targetCount) {
    const remaining = selectedIndices.filter(index => !result.includes(index));
    result.push(...remaining.slice(0, targetCount - result.length));
    result.sort((a, b) => a - b);
  }
  
  return result;
}

/**
 * 使用特征保持算法优化音频数据
 */
export function optimizeAudioDataWithFeaturePreservation(
  originalData: OptimizedAudioAmplitudeData,
  targetSampleCount?: number
): OptimizedAudioAmplitudeData {
  const samples = originalData.samples;
  const originalCount = samples.length;

  // 如果已经是优化过的数据，直接返回
  if (originalData.isOptimized) {
    audioLogger.debug("音频数据已经优化过，跳过重复优化");
    return originalData;
  }

  // 评估性能复杂度
  const complexity = calculatePerformanceComplexity(originalData);

  // 确定目标采样点数
  const finalTargetCount = targetSampleCount || complexity.recommendedSampleCount;

  // 如果不需要优化且没有指定目标采样点数，直接返回原数据
  if (!complexity.needsOptimization && !targetSampleCount) {
    audioLogger.debug("音频数据复杂度较低，无需优化", {
      score: complexity.score,
      level: complexity.level,
      sampleCount: originalCount
    });

    return {
      ...originalData,
      isOptimized: false,
      complexityScore: complexity.score,
      complexityLevel: complexity.level
    };
  }

  if (originalCount <= finalTargetCount) {
    audioLogger.debug("原始采样点数已经足够少，无需优化", {
      originalCount,
      targetCount: finalTargetCount
    });
    return {
      ...originalData,
      isOptimized: false,
      complexityScore: complexity.score,
      complexityLevel: complexity.level
    };
  }

  audioLogger.info("开始优化音频数据", {
    originalCount,
    targetCount: finalTargetCount,
    complexityScore: complexity.score,
    complexityLevel: complexity.level,
    description: complexity.description
  });

  // 步骤1：检测关键特征点
  const keyFeatures = detectKeyFeatures(samples);

  // 步骤2：计算每个点的重要性分数
  const importanceScores = calculateImportanceScores(samples, keyFeatures);

  // 步骤3：选择最重要的点，确保时间分布均匀
  const selectedIndices = selectOptimalPoints(
    importanceScores,
    finalTargetCount,
    keyFeatures
  );

  // 步骤4：构建优化后的数据
  const optimizedSamples = selectedIndices.map(index => samples[index]);

  // 重新计算优化后的最大最小振幅
  const optimizedMaxAmp = Math.max(...optimizedSamples);
  const optimizedMinAmp = Math.min(...optimizedSamples);

  const optimizedData: OptimizedAudioAmplitudeData = {
    ...originalData,
    samples: optimizedSamples,
    max_amplitude: optimizedMaxAmp,
    min_amplitude: optimizedMinAmp,
    // 优化元数据
    isOptimized: true,
    originalSampleCount: originalCount,
    complexityScore: complexity.score,
    complexityLevel: complexity.level,
    optimizationRatio: optimizedSamples.length / originalCount,
    preservedFeatures: keyFeatures.length
  };

  audioLogger.info("音频数据优化完成", {
    originalCount,
    optimizedCount: optimizedSamples.length,
    optimizationRatio: (optimizedSamples.length / originalCount * 100).toFixed(1) + '%',
    preservedFeatures: keyFeatures.length,
    complexityReduction: ((complexity.score - calculatePerformanceComplexity(optimizedData).score) / complexity.score * 100).toFixed(1) + '%'
  });

  return optimizedData;
}

/**
 * 智能音频数据处理入口函数
 * 根据数据特性自动决定是否需要优化
 */
export function processAudioDataIntelligently(
  audioData: OptimizedAudioAmplitudeData,
  options: {
    forceOptimization?: boolean;
    targetSampleCount?: number;
    enableLogging?: boolean;
  } = {}
): OptimizedAudioAmplitudeData {
  const { forceOptimization = false, targetSampleCount, enableLogging = true } = options;

  if (!audioData || !audioData.samples || audioData.samples.length === 0) {
    if (enableLogging) {
      audioLogger.warn("接收到无效的音频数据");
    }
    return audioData;
  }

  // 强制优化模式或指定了目标采样点数
  if (forceOptimization || targetSampleCount) {
    if (enableLogging) {
      audioLogger.debug("强制优化模式，开始处理音频数据", {
        forceOptimization,
        targetSampleCount
      });
    }
    return optimizeAudioDataWithFeaturePreservation(audioData, targetSampleCount);
  }

  // 自动评估模式
  const complexity = calculatePerformanceComplexity(audioData);

  if (enableLogging) {
    audioLogger.debug("音频数据性能评估完成", {
      sampleCount: audioData.samples.length,
      complexityScore: complexity.score,
      complexityLevel: complexity.level,
      needsOptimization: complexity.needsOptimization,
      description: complexity.description
    });
  }

  if (complexity.needsOptimization) {
    return optimizeAudioDataWithFeaturePreservation(audioData, targetSampleCount);
  }

  // 添加复杂度信息但不优化
  return {
    ...audioData,
    isOptimized: false,
    complexityScore: complexity.score,
    complexityLevel: complexity.level
  };
}

/**
 * 获取音频数据的优化统计信息
 */
export function getOptimizationStats(audioData: OptimizedAudioAmplitudeData): {
  isOptimized: boolean;
  originalCount?: number;
  currentCount: number;
  optimizationRatio?: number;
  complexityLevel?: string;
  complexityScore?: number;
  preservedFeatures?: number;
} {
  return {
    isOptimized: audioData.isOptimized || false,
    originalCount: audioData.originalSampleCount,
    currentCount: audioData.samples.length,
    optimizationRatio: audioData.optimizationRatio,
    complexityLevel: audioData.complexityLevel,
    complexityScore: audioData.complexityScore,
    preservedFeatures: audioData.preservedFeatures
  };
}
