<template>
  <div class="audio-optimization-integration-test">
    <div class="test-header">
      <h2>音频优化集成测试</h2>
      <p>测试音频优化功能在实际波形组件中的集成效果</p>
    </div>

    <div class="test-controls">
      <div class="control-group">
        <h3>测试数据</h3>
        <div class="button-group">
          <button @click="loadHighAmplitudeAudio" class="btn primary">
            加载高振幅音频 (卡顿场景)
          </button>
          <button @click="loadLowAmplitudeAudio" class="btn secondary">
            加载低振幅音频 (流畅场景)
          </button>
          <button @click="loadCustomAudio" class="btn">
            加载自定义音频
          </button>
        </div>
      </div>

      <div class="control-group">
        <h3>显示选项</h3>
        <div class="checkbox-group">
          <label class="checkbox-option">
            <input type="checkbox" v-model="showOptimizationControls" />
            显示优化控制面板
          </label>
          <label class="checkbox-option">
            <input type="checkbox" v-model="showPerformanceMonitor" />
            显示性能监控面板
          </label>
          <label class="checkbox-option">
            <input type="checkbox" v-model="enableRealTimeStats" />
            启用实时统计
          </label>
        </div>
      </div>

      <div class="control-group">
        <h3>快速设置</h3>
        <div class="button-group">
          <button @click="setPerformanceMode" class="btn success">
            性能优先模式
          </button>
          <button @click="setBalancedMode" class="btn info">
            平衡模式
          </button>
          <button @click="setQualityMode" class="btn warning">
            质量优先模式
          </button>
        </div>
      </div>
    </div>

    <!-- 当前音频信息 -->
    <div v-if="currentAudioData" class="audio-info">
      <h3>当前音频信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>采样点数:</label>
          <span>{{ currentAudioData.samples.length }}</span>
        </div>
        <div class="info-item">
          <label>振幅范围:</label>
          <span>{{ amplitudeRange.toFixed(3) }}</span>
        </div>
        <div class="info-item">
          <label>时长:</label>
          <span>{{ currentAudioData.duration_ms }}ms</span>
        </div>
        <div class="info-item">
          <label>采样率:</label>
          <span>{{ currentAudioData.sample_rate }}Hz</span>
        </div>
      </div>
    </div>

    <!-- 波形组件 -->
    <div class="waveform-container">
      <InteractiveWaveformCanvas
        :fileUuid="testFileUuid"
        :totalEffectDuration="5000"
        :baselineDuration="1000"
        :availableParentWidth="800"
        :audioDuration="5000"
        :showAudioWaveform="true"
        :showOptimizationControls="showOptimizationControls"
        :showPerformanceMonitor="showPerformanceMonitor"
        :amplitudeDisplayMode="'standard'"
        @visible-time-range-changed="handleVisibleTimeRangeChanged"
      />
    </div>

    <!-- 实时统计 -->
    <div v-if="enableRealTimeStats" class="real-time-stats">
      <h3>实时统计</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-label">渲染次数</div>
          <div class="stat-value">{{ renderCount }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">平均渲染时间</div>
          <div class="stat-value">{{ averageRenderTime.toFixed(1) }}ms</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">最大渲染时间</div>
          <div class="stat-value">{{ maxRenderTime.toFixed(1) }}ms</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">优化状态</div>
          <div class="stat-value" :class="optimizationStatusClass">
            {{ optimizationStatusText }}
          </div>
        </div>
      </div>
    </div>

    <!-- 测试日志 -->
    <div class="test-logs">
      <h3>测试日志</h3>
      <div class="logs-container">
        <div 
          v-for="(log, index) in testLogs" 
          :key="index"
          :class="['log-entry', `log-${log.level}`]"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <div class="logs-controls">
        <button @click="clearLogs" class="btn-small">清除日志</button>
        <button @click="exportLogs" class="btn-small">导出日志</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import InteractiveWaveformCanvas from '../InteractiveWaveformCanvas.vue';
import { useFileWaveformEditorStore } from '@/stores/haptics-editor-store';
import type { OptimizedAudioAmplitudeData } from '../utils/audio-optimization';

// 测试状态
const testFileUuid = 'test-audio-optimization-' + Date.now();
const showOptimizationControls = ref(true);
const showPerformanceMonitor = ref(true);
const enableRealTimeStats = ref(true);

// 音频数据
const currentAudioData = ref<OptimizedAudioAmplitudeData | null>(null);

// 性能统计
const renderCount = ref(0);
const renderTimes = ref<number[]>([]);
const maxRenderTime = ref(0);

// 测试日志
const testLogs = ref<Array<{
  timestamp: number;
  level: 'info' | 'warn' | 'error' | 'success';
  message: string;
}>>([]);

// Store
const waveformStore = useFileWaveformEditorStore(testFileUuid);

// 计算属性
const amplitudeRange = computed(() => {
  if (!currentAudioData.value) return 0;
  return Math.abs(currentAudioData.value.max_amplitude) + Math.abs(currentAudioData.value.min_amplitude);
});

const averageRenderTime = computed(() => {
  if (renderTimes.value.length === 0) return 0;
  const sum = renderTimes.value.reduce((acc, time) => acc + time, 0);
  return sum / renderTimes.value.length;
});

const optimizationStatusText = computed(() => {
  if (!currentAudioData.value) return 'N/A';
  return currentAudioData.value.isOptimized ? '已优化' : '未优化';
});

const optimizationStatusClass = computed(() => {
  if (!currentAudioData.value) return '';
  return currentAudioData.value.isOptimized ? 'status-optimized' : 'status-normal';
});

// 方法
const addLog = (level: 'info' | 'warn' | 'error' | 'success', message: string) => {
  testLogs.value.unshift({
    timestamp: Date.now(),
    level,
    message
  });
  
  // 保持最近100条日志
  if (testLogs.value.length > 100) {
    testLogs.value = testLogs.value.slice(0, 100);
  }
};

const loadHighAmplitudeAudio = () => {
  const samples = Array.from({ length: 4096 }, (_, i) => {
    const sine = Math.sin(i * 0.01) * 2.0;
    const noise = (Math.random() - 0.5) * 0.4;
    return sine + noise;
  });
  
  const audioData: OptimizedAudioAmplitudeData = {
    samples,
    sample_rate: 44100,
    duration_ms: 5000,
    max_amplitude: 2.1412,
    min_amplitude: -2.1674
  };
  
  currentAudioData.value = audioData;
  waveformStore.setAudioData(audioData);
  
  addLog('info', `加载高振幅音频: ${samples.length}个采样点, 振幅范围${amplitudeRange.value.toFixed(3)}`);
};

const loadLowAmplitudeAudio = () => {
  const samples = Array.from({ length: 4096 }, (_, i) => {
    return Math.sin(i * 0.01) * 0.25;
  });
  
  const audioData: OptimizedAudioAmplitudeData = {
    samples,
    sample_rate: 44100,
    duration_ms: 5000,
    max_amplitude: 0.2661,
    min_amplitude: -0.3079
  };
  
  currentAudioData.value = audioData;
  waveformStore.setAudioData(audioData);
  
  addLog('info', `加载低振幅音频: ${samples.length}个采样点, 振幅范围${amplitudeRange.value.toFixed(3)}`);
};

const loadCustomAudio = () => {
  const sampleCount = 2048 + Math.floor(Math.random() * 4096);
  const amplitude = 0.5 + Math.random() * 2.0;
  const frequency = 0.005 + Math.random() * 0.02;
  
  const samples = Array.from({ length: sampleCount }, (_, i) => {
    const sine = Math.sin(i * frequency) * amplitude;
    const noise = (Math.random() - 0.5) * (amplitude * 0.2);
    return sine + noise;
  });
  
  const audioData: OptimizedAudioAmplitudeData = {
    samples,
    sample_rate: 44100,
    duration_ms: 5000,
    max_amplitude: amplitude,
    min_amplitude: -amplitude
  };
  
  currentAudioData.value = audioData;
  waveformStore.setAudioData(audioData);
  
  addLog('info', `加载自定义音频: ${samples.length}个采样点, 振幅范围${amplitudeRange.value.toFixed(3)}`);
};

const setPerformanceMode = () => {
  addLog('success', '切换到性能优先模式');
  // 这里可以通过事件或其他方式通知波形组件更新设置
};

const setBalancedMode = () => {
  addLog('success', '切换到平衡模式');
};

const setQualityMode = () => {
  addLog('success', '切换到质量优先模式');
};

const handleVisibleTimeRangeChanged = (startTime: number, endTime: number) => {
  if (enableRealTimeStats.value) {
    renderCount.value++;
    
    // 模拟渲染时间测量
    const renderTime = Math.random() * 20 + 5; // 5-25ms
    renderTimes.value.push(renderTime);
    
    if (renderTime > maxRenderTime.value) {
      maxRenderTime.value = renderTime;
    }
    
    // 保持最近50次渲染时间
    if (renderTimes.value.length > 50) {
      renderTimes.value = renderTimes.value.slice(-50);
    }
  }
};

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString();
};

const clearLogs = () => {
  testLogs.value = [];
  addLog('info', '日志已清除');
};

const exportLogs = () => {
  const logsData = {
    timestamp: new Date().toISOString(),
    testSession: testFileUuid,
    logs: testLogs.value,
    stats: {
      renderCount: renderCount.value,
      averageRenderTime: averageRenderTime.value,
      maxRenderTime: maxRenderTime.value,
      currentAudio: currentAudioData.value ? {
        sampleCount: currentAudioData.value.samples.length,
        amplitudeRange: amplitudeRange.value,
        isOptimized: currentAudioData.value.isOptimized
      } : null
    }
  };
  
  const blob = new Blob([JSON.stringify(logsData, null, 2)], { 
    type: 'application/json' 
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `audio-optimization-test-${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  addLog('success', '测试日志已导出');
};

// 生命周期
onMounted(() => {
  addLog('info', '音频优化集成测试已启动');
  loadHighAmplitudeAudio(); // 默认加载高振幅音频
});

onUnmounted(() => {
  addLog('info', '音频优化集成测试已结束');
});
</script>

<style scoped>
.audio-optimization-integration-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.test-header p {
  color: #666;
  font-size: 16px;
}

.test-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.control-group {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.control-group h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.btn.primary { background: #007bff; color: white; }
.btn.secondary { background: #6c757d; color: white; }
.btn.success { background: #28a745; color: white; }
.btn.info { background: #17a2b8; color: white; }
.btn.warning { background: #ffc107; color: #212529; }

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  cursor: pointer;
}

.audio-info {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.audio-info h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.info-item label {
  color: #666;
}

.info-item span {
  font-weight: 500;
  color: #333;
}

.waveform-container {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  min-height: 400px;
}

.real-time-stats {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.real-time-stats h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.stat-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.stat-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.status-optimized { color: #28a745; }
.status-normal { color: #6c757d; }

.test-logs {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.test-logs h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 12px;
}

.log-entry {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  font-family: 'Consolas', monospace;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-info { color: #17a2b8; }
.log-success { color: #28a745; }
.log-warn { color: #ffc107; }
.log-error { color: #dc3545; }

.logs-controls {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.btn-small:hover {
  background: #f8f9fa;
}
</style>
