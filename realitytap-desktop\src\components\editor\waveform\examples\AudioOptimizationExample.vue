<template>
  <div class="audio-optimization-example">
    <div class="example-header">
      <h2>音频优化功能演示</h2>
      <p>展示智能音频数据优化的效果和性能提升</p>
    </div>

    <div class="example-content">
      <!-- 测试数据生成 -->
      <div class="test-data-section">
        <h3>测试数据生成</h3>
        <div class="controls">
          <button @click="generateHighAmplitudeAudio" class="btn primary">
            生成高振幅音频 (类似音频A)
          </button>
          <button @click="generateLowAmplitudeAudio" class="btn secondary">
            生成低振幅音频 (类似音频B)
          </button>
          <button @click="generateCustomAudio" class="btn">
            自定义音频
          </button>
        </div>
        
        <div v-if="showCustomControls" class="custom-controls">
          <label>
            采样点数: 
            <input v-model.number="customSampleCount" type="number" min="100" max="10000" />
          </label>
          <label>
            最大振幅: 
            <input v-model.number="customMaxAmplitude" type="number" min="0.1" max="5.0" step="0.1" />
          </label>
          <label>
            频率: 
            <input v-model.number="customFrequency" type="number" min="0.001" max="0.1" step="0.001" />
          </label>
        </div>
      </div>

      <!-- 当前音频信息 -->
      <div v-if="currentAudio" class="audio-info-section">
        <h3>当前音频信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>采样点数:</label>
            <span>{{ currentAudio.samples.length }}</span>
          </div>
          <div class="info-item">
            <label>振幅范围:</label>
            <span>{{ amplitudeRange.toFixed(3) }}</span>
          </div>
          <div class="info-item">
            <label>时长:</label>
            <span>{{ currentAudio.duration_ms }}ms</span>
          </div>
          <div class="info-item">
            <label>采样率:</label>
            <span>{{ currentAudio.sample_rate }}Hz</span>
          </div>
        </div>
      </div>

      <!-- 性能评估结果 -->
      <div v-if="complexityResult" class="complexity-section">
        <h3>性能复杂度评估</h3>
        <div class="complexity-card" :class="`complexity-${complexityResult.level}`">
          <div class="complexity-header">
            <span class="complexity-level">{{ complexityLevelText }}</span>
            <span class="complexity-score">分数: {{ complexityResult.score.toFixed(0) }}</span>
          </div>
          <div class="complexity-description">{{ complexityResult.description }}</div>
          <div class="complexity-details">
            <div class="detail-item">
              <label>需要优化:</label>
              <span :class="complexityResult.needsOptimization ? 'text-warning' : 'text-success'">
                {{ complexityResult.needsOptimization ? '是' : '否' }}
              </span>
            </div>
            <div class="detail-item">
              <label>推荐采样点数:</label>
              <span>{{ complexityResult.recommendedSampleCount }}</span>
            </div>
            <div class="detail-item">
              <label>振幅范围:</label>
              <span>{{ complexityResult.amplitudeRange.toFixed(3) }}</span>
            </div>
            <div class="detail-item">
              <label>变化率:</label>
              <span>{{ complexityResult.variationRate.toFixed(3) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化操作 -->
      <div v-if="currentAudio" class="optimization-section">
        <h3>优化操作</h3>
        <div class="optimization-controls">
          <button @click="performAutoOptimization" class="btn primary">
            自动优化
          </button>
          <button @click="performForceOptimization" class="btn warning">
            强制优化
          </button>
          <button @click="performCustomOptimization" class="btn secondary">
            自定义优化
          </button>
        </div>
        
        <div v-if="showCustomOptimization" class="custom-optimization">
          <label>
            目标采样点数: 
            <input v-model.number="targetSampleCount" type="number" min="100" :max="currentAudio.samples.length" />
          </label>
          <button @click="applyCustomOptimization" class="btn">应用</button>
        </div>
      </div>

      <!-- 优化结果对比 -->
      <div v-if="optimizedAudio" class="results-section">
        <h3>优化结果对比</h3>
        <div class="comparison-grid">
          <div class="comparison-item">
            <h4>原始数据</h4>
            <div class="stats">
              <div>采样点数: {{ currentAudio.samples.length }}</div>
              <div>内存占用: {{ (currentAudio.samples.length * 8 / 1024).toFixed(1) }}KB</div>
              <div>复杂度: {{ complexityResult?.score.toFixed(0) }}</div>
            </div>
          </div>
          <div class="comparison-item">
            <h4>优化后数据</h4>
            <div class="stats">
              <div>采样点数: {{ optimizedAudio.samples.length }}</div>
              <div>内存占用: {{ (optimizedAudio.samples.length * 8 / 1024).toFixed(1) }}KB</div>
              <div>复杂度: {{ optimizedComplexity?.score.toFixed(0) }}</div>
            </div>
          </div>
        </div>
        
        <div class="optimization-stats">
          <div class="stat-item">
            <label>优化比例:</label>
            <span class="stat-value">{{ optimizationRatio }}%</span>
          </div>
          <div class="stat-item">
            <label>内存节省:</label>
            <span class="stat-value">{{ memorySaving }}%</span>
          </div>
          <div class="stat-item">
            <label>复杂度降低:</label>
            <span class="stat-value">{{ complexityReduction }}%</span>
          </div>
          <div class="stat-item">
            <label>保留特征点:</label>
            <span class="stat-value">{{ optimizedAudio.preservedFeatures || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 性能测试 -->
      <div v-if="currentAudio" class="performance-section">
        <h3>性能测试</h3>
        <div class="performance-controls">
          <button @click="runPerformanceTest" :disabled="isRunningTest" class="btn primary">
            {{ isRunningTest ? '测试中...' : '运行性能测试' }}
          </button>
          <button @click="clearPerformanceResults" class="btn secondary">
            清除结果
          </button>
        </div>
        
        <div v-if="performanceResults.length > 0" class="performance-results">
          <h4>测试结果</h4>
          <div class="results-table">
            <div class="table-header">
              <span>数据类型</span>
              <span>渲染时间</span>
              <span>FPS</span>
              <span>内存使用</span>
            </div>
            <div v-for="result in performanceResults" :key="result.type" class="table-row">
              <span>{{ result.type }}</span>
              <span :class="getPerformanceClass(result.renderTime)">{{ result.renderTime.toFixed(2) }}ms</span>
              <span :class="getPerformanceClass(result.fps, true)">{{ result.fps.toFixed(0) }}</span>
              <span>{{ (result.memoryUsage / 1024 / 1024).toFixed(1) }}MB</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  calculatePerformanceComplexity,
  processAudioDataIntelligently,
  optimizeAudioDataWithFeaturePreservation,
  type OptimizedAudioAmplitudeData,
  type PerformanceComplexity
} from '../utils/audio-optimization';

// 状态
const currentAudio = ref<OptimizedAudioAmplitudeData | null>(null);
const optimizedAudio = ref<OptimizedAudioAmplitudeData | null>(null);
const complexityResult = ref<PerformanceComplexity | null>(null);
const optimizedComplexity = ref<PerformanceComplexity | null>(null);

// 控制状态
const showCustomControls = ref(false);
const showCustomOptimization = ref(false);
const isRunningTest = ref(false);

// 自定义参数
const customSampleCount = ref(4096);
const customMaxAmplitude = ref(2.0);
const customFrequency = ref(0.01);
const targetSampleCount = ref(2000);

// 性能测试结果
const performanceResults = ref<Array<{
  type: string;
  renderTime: number;
  fps: number;
  memoryUsage: number;
}>>([]);

// 计算属性
const amplitudeRange = computed(() => {
  if (!currentAudio.value) return 0;
  return Math.abs(currentAudio.value.max_amplitude) + Math.abs(currentAudio.value.min_amplitude);
});

const complexityLevelText = computed(() => {
  if (!complexityResult.value) return '';
  const levelMap = {
    low: '低复杂度',
    medium: '中等复杂度',
    high: '高复杂度',
    extreme: '极高复杂度'
  };
  return levelMap[complexityResult.value.level];
});

const optimizationRatio = computed(() => {
  if (!currentAudio.value || !optimizedAudio.value) return 0;
  return ((optimizedAudio.value.samples.length / currentAudio.value.samples.length) * 100).toFixed(1);
});

const memorySaving = computed(() => {
  if (!currentAudio.value || !optimizedAudio.value) return 0;
  const originalSize = currentAudio.value.samples.length * 8;
  const optimizedSize = optimizedAudio.value.samples.length * 8;
  return (((originalSize - optimizedSize) / originalSize) * 100).toFixed(1);
});

const complexityReduction = computed(() => {
  if (!complexityResult.value || !optimizedComplexity.value) return 0;
  const reduction = ((complexityResult.value.score - optimizedComplexity.value.score) / complexityResult.value.score) * 100;
  return reduction.toFixed(1);
});

// 方法
const generateHighAmplitudeAudio = () => {
  const samples = Array.from({ length: 4096 }, (_, i) => {
    const sine = Math.sin(i * 0.01) * 2.0;
    const noise = (Math.random() - 0.5) * 0.4;
    return sine + noise;
  });
  
  currentAudio.value = {
    samples,
    sample_rate: 44100,
    duration_ms: 5000,
    max_amplitude: 2.1412,
    min_amplitude: -2.1674
  };
  
  evaluateComplexity();
  clearOptimizationResults();
};

const generateLowAmplitudeAudio = () => {
  const samples = Array.from({ length: 4096 }, (_, i) => {
    return Math.sin(i * 0.01) * 0.25;
  });
  
  currentAudio.value = {
    samples,
    sample_rate: 44100,
    duration_ms: 5000,
    max_amplitude: 0.2661,
    min_amplitude: -0.3079
  };
  
  evaluateComplexity();
  clearOptimizationResults();
};

const generateCustomAudio = () => {
  showCustomControls.value = !showCustomControls.value;
  if (showCustomControls.value) {
    const samples = Array.from({ length: customSampleCount.value }, (_, i) => {
      const sine = Math.sin(i * customFrequency.value) * customMaxAmplitude.value;
      const noise = (Math.random() - 0.5) * (customMaxAmplitude.value * 0.1);
      return sine + noise;
    });
    
    currentAudio.value = {
      samples,
      sample_rate: 44100,
      duration_ms: 5000,
      max_amplitude: customMaxAmplitude.value,
      min_amplitude: -customMaxAmplitude.value
    };
    
    evaluateComplexity();
    clearOptimizationResults();
  }
};

const evaluateComplexity = () => {
  if (!currentAudio.value) return;
  complexityResult.value = calculatePerformanceComplexity(currentAudio.value);
};

const clearOptimizationResults = () => {
  optimizedAudio.value = null;
  optimizedComplexity.value = null;
};

const performAutoOptimization = () => {
  if (!currentAudio.value) return;
  
  optimizedAudio.value = processAudioDataIntelligently(currentAudio.value);
  if (optimizedAudio.value) {
    optimizedComplexity.value = calculatePerformanceComplexity(optimizedAudio.value);
  }
};

const performForceOptimization = () => {
  if (!currentAudio.value) return;
  
  optimizedAudio.value = processAudioDataIntelligently(currentAudio.value, {
    forceOptimization: true
  });
  if (optimizedAudio.value) {
    optimizedComplexity.value = calculatePerformanceComplexity(optimizedAudio.value);
  }
};

const performCustomOptimization = () => {
  showCustomOptimization.value = !showCustomOptimization.value;
};

const applyCustomOptimization = () => {
  if (!currentAudio.value) return;
  
  optimizedAudio.value = optimizeAudioDataWithFeaturePreservation(
    currentAudio.value,
    targetSampleCount.value
  );
  if (optimizedAudio.value) {
    optimizedComplexity.value = calculatePerformanceComplexity(optimizedAudio.value);
  }
  showCustomOptimization.value = false;
};

const runPerformanceTest = async () => {
  if (!currentAudio.value || isRunningTest.value) return;
  
  isRunningTest.value = true;
  performanceResults.value = [];
  
  try {
    // 测试原始数据性能
    const originalResult = await simulateRenderingPerformance(currentAudio.value, '原始数据');
    performanceResults.value.push(originalResult);
    
    // 如果有优化数据，测试优化数据性能
    if (optimizedAudio.value) {
      const optimizedResult = await simulateRenderingPerformance(optimizedAudio.value, '优化数据');
      performanceResults.value.push(optimizedResult);
    }
  } finally {
    isRunningTest.value = false;
  }
};

const simulateRenderingPerformance = async (audioData: OptimizedAudioAmplitudeData, type: string) => {
  const startTime = performance.now();
  const startMemory = (performance as any).memory?.usedJSHeapSize || 0;
  
  // 模拟渲染过程
  const canvas = document.createElement('canvas');
  canvas.width = 800;
  canvas.height = 400;
  const ctx = canvas.getContext('2d')!;
  
  // 模拟多次渲染以获得平均性能
  const renderCount = 10;
  let totalRenderTime = 0;
  
  for (let i = 0; i < renderCount; i++) {
    const renderStart = performance.now();
    
    // 模拟波形绘制
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.beginPath();
    
    for (let j = 0; j < audioData.samples.length; j++) {
      const x = (j / audioData.samples.length) * canvas.width;
      const y = (audioData.samples[j] + 1) * canvas.height / 2;
      
      if (j === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    ctx.stroke();
    
    totalRenderTime += performance.now() - renderStart;
    
    // 让出控制权，避免阻塞UI
    await new Promise(resolve => setTimeout(resolve, 1));
  }
  
  const avgRenderTime = totalRenderTime / renderCount;
  const fps = 1000 / avgRenderTime;
  const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
  
  return {
    type,
    renderTime: avgRenderTime,
    fps,
    memoryUsage: endMemory - startMemory
  };
};

const clearPerformanceResults = () => {
  performanceResults.value = [];
};

const getPerformanceClass = (value: number, isFps = false) => {
  if (isFps) {
    if (value >= 30) return 'performance-good';
    if (value >= 20) return 'performance-ok';
    return 'performance-bad';
  } else {
    if (value < 16) return 'performance-good';
    if (value < 33) return 'performance-ok';
    return 'performance-bad';
  }
};

// 初始化
onMounted(() => {
  generateHighAmplitudeAudio();
});
</script>

<style scoped>
.audio-optimization-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-header {
  text-align: center;
  margin-bottom: 30px;
}

.example-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.example-header p {
  color: #666;
  font-size: 16px;
}

.example-content > div {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.example-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.controls, .optimization-controls, .performance-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn.primary {
  background: #007bff;
  color: white;
}

.btn.primary:hover {
  background: #0056b3;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.warning {
  background: #ffc107;
  color: #212529;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.custom-controls, .custom-optimization {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.custom-controls label, .custom-optimization label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.custom-controls input, .custom-optimization input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100px;
}

.info-grid, .comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.info-item, .detail-item, .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child, .detail-item:last-child, .stat-item:last-child {
  border-bottom: none;
}

.complexity-card {
  border-radius: 8px;
  padding: 20px;
  margin: 15px 0;
}

.complexity-low {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border-left: 4px solid #28a745;
}

.complexity-medium {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-left: 4px solid #ffc107;
}

.complexity-high {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  border-left: 4px solid #dc3545;
}

.complexity-extreme {
  background: linear-gradient(135deg, #f8d7da, #f1b0b7);
  border-left: 4px solid #721c24;
}

.complexity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.complexity-level {
  font-weight: 600;
  font-size: 16px;
}

.complexity-score {
  font-family: 'Consolas', monospace;
  font-weight: 500;
}

.complexity-description {
  margin-bottom: 15px;
  font-style: italic;
  color: #666;
}

.text-success { color: #28a745; }
.text-warning { color: #ffc107; }
.text-danger { color: #dc3545; }

.stat-value {
  font-weight: 600;
  color: #007bff;
}

.optimization-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.comparison-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.comparison-item h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.stats div {
  margin-bottom: 5px;
  font-size: 14px;
}

.results-table {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 10px;
  margin-top: 15px;
}

.table-header {
  display: contents;
  font-weight: 600;
  background: #f8f9fa;
}

.table-header span {
  padding: 10px;
  background: #e9ecef;
  border-radius: 4px;
}

.table-row {
  display: contents;
}

.table-row span {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.performance-good { color: #28a745; font-weight: 600; }
.performance-ok { color: #ffc107; font-weight: 600; }
.performance-bad { color: #dc3545; font-weight: 600; }
</style>
