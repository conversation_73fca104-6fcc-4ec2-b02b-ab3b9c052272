<template>
  <div v-if="showDebugPanel" class="audio-optimization-debug-panel">
    <div class="debug-header">
      <h3>音频优化调试面板</h3>
      <button @click="togglePanel" class="close-btn">×</button>
    </div>
    
    <div class="debug-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h4>基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>采样点数:</label>
            <span>{{ stats?.currentCount || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <label>原始点数:</label>
            <span>{{ stats?.originalCount || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <label>优化状态:</label>
            <span :class="optimizationStatusClass">{{ optimizationStatusText }}</span>
          </div>
          <div class="info-item">
            <label>复杂度等级:</label>
            <span :class="complexityLevelClass">{{ stats?.complexityLevel || 'N/A' }}</span>
          </div>
        </div>
      </div>

      <!-- 优化统计 -->
      <div v-if="stats?.isOptimized" class="optimization-section">
        <h4>优化统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <label>优化比例:</label>
            <span class="stat-value">{{ optimizationRatioText }}</span>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${(stats.optimizationRatio || 0) * 100}%` }"
              ></div>
            </div>
          </div>
          <div class="stat-item">
            <label>保留特征点:</label>
            <span class="stat-value">{{ stats.preservedFeatures || 0 }}</span>
          </div>
          <div class="stat-item">
            <label>复杂度分数:</label>
            <span class="stat-value">{{ complexityScoreText }}</span>
          </div>
        </div>
      </div>

      <!-- 性能指标 -->
      <div class="performance-section">
        <h4>性能指标</h4>
        <div class="performance-grid">
          <div class="performance-item">
            <label>渲染时间:</label>
            <span :class="renderTimeClass">{{ renderTimeText }}</span>
          </div>
          <div class="performance-item">
            <label>FPS:</label>
            <span :class="fpsClass">{{ fpsText }}</span>
          </div>
          <div class="performance-item">
            <label>内存使用:</label>
            <span>{{ memoryUsageText }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <h4>操作</h4>
        <div class="action-buttons">
          <button 
            @click="forceReoptimize" 
            :disabled="!hasAudioData"
            class="action-btn primary"
          >
            强制重新优化
          </button>
          <button 
            @click="toggleOptimization" 
            :disabled="!hasAudioData"
            class="action-btn"
          >
            {{ optimizationEnabled ? '禁用优化' : '启用优化' }}
          </button>
          <button 
            @click="exportStats" 
            :disabled="!hasAudioData"
            class="action-btn secondary"
          >
            导出统计
          </button>
        </div>
      </div>

      <!-- 实时日志 -->
      <div class="logs-section">
        <h4>实时日志</h4>
        <div class="logs-container">
          <div 
            v-for="(log, index) in recentLogs" 
            :key="index"
            :class="['log-entry', `log-${log.level}`]"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import { getOptimizationStats } from '../utils/audio-optimization';

// Props
interface Props {
  audioData?: any;
  showDebugPanel?: boolean;
  hasAudioData?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showDebugPanel: false,
  hasAudioData: false
});

// Emits
const emit = defineEmits<{
  'toggle-panel': [];
  'force-reoptimize': [];
  'toggle-optimization': [enabled: boolean];
  'export-stats': [stats: any];
}>();

// 状态
const optimizationEnabled = ref(true);
const renderTime = ref(0);
const fps = ref(0);
const memoryUsage = ref(0);
const recentLogs = ref<Array<{
  timestamp: number;
  level: 'info' | 'warn' | 'error';
  message: string;
}>>([]);

// 计算属性
const stats = computed(() => {
  if (!props.audioData) return null;
  return getOptimizationStats(props.audioData);
});

const optimizationStatusText = computed(() => {
  if (!stats.value) return '无数据';
  return stats.value.isOptimized ? '已优化' : '未优化';
});

const optimizationStatusClass = computed(() => {
  if (!stats.value) return 'status-none';
  return stats.value.isOptimized ? 'status-optimized' : 'status-normal';
});

const complexityLevelClass = computed(() => {
  const level = stats.value?.complexityLevel;
  return `complexity-${level}`;
});

const optimizationRatioText = computed(() => {
  if (!stats.value?.optimizationRatio) return 'N/A';
  return `${(stats.value.optimizationRatio * 100).toFixed(1)}%`;
});

const complexityScoreText = computed(() => {
  if (!stats.value?.complexityScore) return 'N/A';
  return stats.value.complexityScore.toFixed(0);
});

const renderTimeText = computed(() => {
  return `${renderTime.value.toFixed(2)}ms`;
});

const renderTimeClass = computed(() => {
  if (renderTime.value < 16) return 'performance-good';
  if (renderTime.value < 33) return 'performance-ok';
  return 'performance-bad';
});

const fpsText = computed(() => {
  return `${fps.value.toFixed(0)}`;
});

const fpsClass = computed(() => {
  if (fps.value >= 30) return 'performance-good';
  if (fps.value >= 20) return 'performance-ok';
  return 'performance-bad';
});

const memoryUsageText = computed(() => {
  return `${(memoryUsage.value / 1024 / 1024).toFixed(1)}MB`;
});

// 方法
const togglePanel = () => {
  emit('toggle-panel');
};

const forceReoptimize = () => {
  addLog('info', '强制重新优化音频数据');
  emit('force-reoptimize');
};

const toggleOptimization = () => {
  optimizationEnabled.value = !optimizationEnabled.value;
  addLog('info', `${optimizationEnabled.value ? '启用' : '禁用'}音频优化`);
  emit('toggle-optimization', optimizationEnabled.value);
};

const exportStats = () => {
  if (stats.value) {
    const statsData = {
      timestamp: new Date().toISOString(),
      audioStats: stats.value,
      performance: {
        renderTime: renderTime.value,
        fps: fps.value,
        memoryUsage: memoryUsage.value
      }
    };
    
    addLog('info', '导出优化统计数据');
    emit('export-stats', statsData);
    
    // 下载JSON文件
    const blob = new Blob([JSON.stringify(statsData, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audio-optimization-stats-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }
};

const addLog = (level: 'info' | 'warn' | 'error', message: string) => {
  recentLogs.value.unshift({
    timestamp: Date.now(),
    level,
    message
  });
  
  // 保持最近50条日志
  if (recentLogs.value.length > 50) {
    recentLogs.value = recentLogs.value.slice(0, 50);
  }
};

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString();
};

// 性能监控
let performanceMonitor: number | null = null;
let lastFrameTime = performance.now();
let frameCount = 0;

const startPerformanceMonitoring = () => {
  const monitor = () => {
    const now = performance.now();
    frameCount++;
    
    // 每秒更新一次FPS
    if (now - lastFrameTime >= 1000) {
      fps.value = frameCount;
      frameCount = 0;
      lastFrameTime = now;
      
      // 更新内存使用（如果可用）
      if ('memory' in performance) {
        memoryUsage.value = (performance as any).memory.usedJSHeapSize;
      }
    }
    
    performanceMonitor = requestAnimationFrame(monitor);
  };
  
  performanceMonitor = requestAnimationFrame(monitor);
};

const stopPerformanceMonitoring = () => {
  if (performanceMonitor) {
    cancelAnimationFrame(performanceMonitor);
    performanceMonitor = null;
  }
};

// 监听音频数据变化
watch(() => props.audioData, (newData, oldData) => {
  if (newData !== oldData) {
    addLog('info', '音频数据已更新');
  }
}, { deep: true });

// 生命周期
onMounted(() => {
  startPerformanceMonitoring();
  addLog('info', '调试面板已启动');
});

onUnmounted(() => {
  stopPerformanceMonitoring();
});
</script>

<style scoped>
.audio-optimization-debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  overflow: hidden;
  z-index: 1000;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.debug-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-content {
  padding: 16px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.info-section, .optimization-section, .performance-section, .actions-section, .logs-section {
  margin-bottom: 20px;
}

.info-section h4, .optimization-section h4, .performance-section h4, .actions-section h4, .logs-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #ccc;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 4px;
}

.info-grid, .stats-grid, .performance-grid {
  display: grid;
  gap: 8px;
}

.info-item, .stat-item, .performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item label, .stat-item label, .performance-item label {
  color: #aaa;
  font-size: 11px;
}

.stat-value {
  font-weight: 600;
  color: #4CAF50;
}

.status-optimized {
  color: #4CAF50;
  font-weight: 600;
}

.status-normal {
  color: #FFC107;
}

.status-none {
  color: #666;
}

.complexity-low { color: #4CAF50; }
.complexity-medium { color: #FFC107; }
.complexity-high { color: #FF9800; }
.complexity-extreme { color: #F44336; }

.performance-good { color: #4CAF50; }
.performance-ok { color: #FFC107; }
.performance-bad { color: #F44336; }

.progress-bar {
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4CAF50;
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.primary {
  background: #2196F3;
  border-color: #2196F3;
}

.action-btn.secondary {
  background: #666;
  border-color: #666;
}

.logs-container {
  max-height: 150px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 8px;
}

.log-entry {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 10px;
}

.log-time {
  color: #666;
  min-width: 60px;
}

.log-message {
  flex: 1;
}

.log-info { color: #4CAF50; }
.log-warn { color: #FFC107; }
.log-error { color: #F44336; }
</style>
