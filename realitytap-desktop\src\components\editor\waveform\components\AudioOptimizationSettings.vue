<template>
  <div class="audio-optimization-settings">
    <div class="settings-header">
      <h3>音频优化设置</h3>
      <button @click="toggleExpanded" class="toggle-btn">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-if="isExpanded" class="settings-content">
      <!-- 优化模式选择 -->
      <div class="setting-group">
        <label class="setting-label">优化模式</label>
        <div class="radio-group">
          <label class="radio-option">
            <input 
              type="radio" 
              :value="'auto'" 
              v-model="localSettings.optimizationMode"
              @change="handleSettingChange"
            />
            <span>自动优化</span>
            <small>根据复杂度自动决定是否优化</small>
          </label>
          <label class="radio-option">
            <input 
              type="radio" 
              :value="'force'" 
              v-model="localSettings.optimizationMode"
              @change="handleSettingChange"
            />
            <span>强制优化</span>
            <small>始终对音频数据进行优化</small>
          </label>
          <label class="radio-option">
            <input 
              type="radio" 
              :value="'disabled'" 
              v-model="localSettings.optimizationMode"
              @change="handleSettingChange"
            />
            <span>禁用优化</span>
            <small>不对音频数据进行任何优化</small>
          </label>
        </div>
      </div>

      <!-- 性能目标选择 -->
      <div class="setting-group">
        <label class="setting-label">性能目标</label>
        <div class="radio-group">
          <label class="radio-option">
            <input 
              type="radio" 
              :value="'performance'" 
              v-model="localSettings.performanceTarget"
              @change="handleSettingChange"
              :disabled="localSettings.optimizationMode === 'disabled'"
            />
            <span>性能优先</span>
            <small>最大化渲染性能，保留约1600个采样点</small>
          </label>
          <label class="radio-option">
            <input 
              type="radio" 
              :value="'balanced'" 
              v-model="localSettings.performanceTarget"
              @change="handleSettingChange"
              :disabled="localSettings.optimizationMode === 'disabled'"
            />
            <span>平衡模式</span>
            <small>平衡性能和质量，保留约2400个采样点</small>
          </label>
          <label class="radio-option">
            <input 
              type="radio" 
              :value="'quality'" 
              v-model="localSettings.performanceTarget"
              @change="handleSettingChange"
              :disabled="localSettings.optimizationMode === 'disabled'"
            />
            <span>质量优先</span>
            <small>保持最佳视觉质量，根据复杂度动态调整</small>
          </label>
        </div>
      </div>

      <!-- 高级设置 -->
      <div class="setting-group">
        <label class="setting-label">
          <input 
            type="checkbox" 
            v-model="showAdvancedSettings"
          />
          高级设置
        </label>
      </div>

      <div v-if="showAdvancedSettings" class="advanced-settings">
        <!-- 自定义采样点数 -->
        <div class="setting-item">
          <label class="setting-label">自定义采样点数</label>
          <div class="input-group">
            <input 
              type="number" 
              v-model.number="localSettings.customSampleCount"
              :min="100"
              :max="8192"
              :disabled="localSettings.optimizationMode === 'disabled' || localSettings.performanceTarget !== 'custom'"
              @change="handleSettingChange"
            />
            <button 
              @click="setCustomTarget"
              :disabled="localSettings.optimizationMode === 'disabled'"
              class="btn-small"
            >
              使用自定义
            </button>
          </div>
        </div>

        <!-- 复杂度阈值调整 -->
        <div class="setting-item">
          <label class="setting-label">复杂度阈值调整</label>
          <div class="threshold-controls">
            <div class="threshold-item">
              <label>低复杂度阈值:</label>
              <input 
                type="number" 
                v-model.number="localSettings.thresholds.low"
                :min="1000"
                :max="5000"
                @change="handleSettingChange"
              />
            </div>
            <div class="threshold-item">
              <label>中等复杂度阈值:</label>
              <input 
                type="number" 
                v-model.number="localSettings.thresholds.medium"
                :min="5000"
                :max="12000"
                @change="handleSettingChange"
              />
            </div>
            <div class="threshold-item">
              <label>高复杂度阈值:</label>
              <input 
                type="number" 
                v-model.number="localSettings.thresholds.high"
                :min="12000"
                :max="25000"
                @change="handleSettingChange"
              />
            </div>
          </div>
        </div>

        <!-- 特征保持设置 -->
        <div class="setting-item">
          <label class="setting-label">特征保持设置</label>
          <div class="feature-controls">
            <label class="checkbox-option">
              <input 
                type="checkbox" 
                v-model="localSettings.featurePreservation.preservePeaks"
                @change="handleSettingChange"
              />
              保留波峰
            </label>
            <label class="checkbox-option">
              <input 
                type="checkbox" 
                v-model="localSettings.featurePreservation.preserveValleys"
                @change="handleSettingChange"
              />
              保留波谷
            </label>
            <label class="checkbox-option">
              <input 
                type="checkbox" 
                v-model="localSettings.featurePreservation.preserveInflectionPoints"
                @change="handleSettingChange"
              />
              保留转折点
            </label>
            <label class="checkbox-option">
              <input 
                type="checkbox" 
                v-model="localSettings.featurePreservation.preserveBoundaries"
                @change="handleSettingChange"
              />
              保留边界点
            </label>
          </div>
        </div>
      </div>

      <!-- 当前状态显示 -->
      <div v-if="currentStats" class="current-status">
        <h4>当前状态</h4>
        <div class="status-grid">
          <div class="status-item">
            <label>原始采样点数:</label>
            <span>{{ currentStats.originalCount || 'N/A' }}</span>
          </div>
          <div class="status-item">
            <label>当前采样点数:</label>
            <span>{{ currentStats.currentCount || 'N/A' }}</span>
          </div>
          <div class="status-item">
            <label>优化状态:</label>
            <span :class="currentStats.isOptimized ? 'status-optimized' : 'status-normal'">
              {{ currentStats.isOptimized ? '已优化' : '未优化' }}
            </span>
          </div>
          <div class="status-item">
            <label>复杂度等级:</label>
            <span :class="`complexity-${currentStats.complexityLevel}`">
              {{ getComplexityLevelText(currentStats.complexityLevel) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button @click="resetToDefaults" class="btn-secondary">
          恢复默认
        </button>
        <button @click="applySettings" class="btn-primary">
          应用设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';

// Props
interface Props {
  currentSettings?: {
    optimizationMode?: 'auto' | 'force' | 'disabled';
    performanceTarget?: 'performance' | 'balanced' | 'quality' | 'custom';
    customSampleCount?: number;
    thresholds?: {
      low: number;
      medium: number;
      high: number;
    };
    featurePreservation?: {
      preservePeaks: boolean;
      preserveValleys: boolean;
      preserveInflectionPoints: boolean;
      preserveBoundaries: boolean;
    };
  };
  currentStats?: {
    originalCount?: number;
    currentCount?: number;
    isOptimized?: boolean;
    complexityLevel?: 'low' | 'medium' | 'high' | 'extreme';
    complexityScore?: number;
  };
}

const props = withDefaults(defineProps<Props>(), {
  currentSettings: () => ({
    optimizationMode: 'auto',
    performanceTarget: 'balanced',
    customSampleCount: 2000,
    thresholds: {
      low: 3000,
      medium: 8000,
      high: 15000
    },
    featurePreservation: {
      preservePeaks: true,
      preserveValleys: true,
      preserveInflectionPoints: true,
      preserveBoundaries: true
    }
  })
});

// Emits
const emit = defineEmits<{
  'settings-changed': [settings: any];
  'apply-settings': [settings: any];
  'reset-settings': [];
}>();

// 状态
const isExpanded = ref(false);
const showAdvancedSettings = ref(false);

// 本地设置状态
const localSettings = reactive({
  optimizationMode: props.currentSettings?.optimizationMode || 'auto',
  performanceTarget: props.currentSettings?.performanceTarget || 'balanced',
  customSampleCount: props.currentSettings?.customSampleCount || 2000,
  thresholds: {
    low: props.currentSettings?.thresholds?.low || 3000,
    medium: props.currentSettings?.thresholds?.medium || 8000,
    high: props.currentSettings?.thresholds?.high || 15000
  },
  featurePreservation: {
    preservePeaks: props.currentSettings?.featurePreservation?.preservePeaks ?? true,
    preserveValleys: props.currentSettings?.featurePreservation?.preserveValleys ?? true,
    preserveInflectionPoints: props.currentSettings?.featurePreservation?.preserveInflectionPoints ?? true,
    preserveBoundaries: props.currentSettings?.featurePreservation?.preserveBoundaries ?? true
  }
});

// 计算属性
const getComplexityLevelText = (level?: string) => {
  const levelMap = {
    low: '低复杂度',
    medium: '中等复杂度',
    high: '高复杂度',
    extreme: '极高复杂度'
  };
  return levelMap[level as keyof typeof levelMap] || 'N/A';
};

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const handleSettingChange = () => {
  emit('settings-changed', { ...localSettings });
};

const setCustomTarget = () => {
  localSettings.performanceTarget = 'custom';
  handleSettingChange();
};

const resetToDefaults = () => {
  localSettings.optimizationMode = 'auto';
  localSettings.performanceTarget = 'balanced';
  localSettings.customSampleCount = 2000;
  localSettings.thresholds = {
    low: 3000,
    medium: 8000,
    high: 15000
  };
  localSettings.featurePreservation = {
    preservePeaks: true,
    preserveValleys: true,
    preserveInflectionPoints: true,
    preserveBoundaries: true
  };
  
  emit('reset-settings');
  handleSettingChange();
};

const applySettings = () => {
  emit('apply-settings', { ...localSettings });
};

// 监听props变化，同步到本地状态
watch(() => props.currentSettings, (newSettings) => {
  if (newSettings) {
    Object.assign(localSettings, newSettings);
  }
}, { deep: true });
</script>

<style scoped>
.audio-optimization-settings {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
}

.settings-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toggle-btn {
  background: none;
  border: 1px solid #007bff;
  color: #007bff;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.toggle-btn:hover {
  background: #007bff;
  color: white;
}

.settings-content {
  padding: 16px;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  font-size: 13px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-option:hover {
  background: #f8f9fa;
}

.radio-option input[type="radio"] {
  margin-top: 2px;
}

.radio-option span {
  font-weight: 500;
  color: #333;
}

.radio-option small {
  display: block;
  color: #666;
  font-size: 11px;
  margin-top: 2px;
}

.advanced-settings {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-top: 12px;
}

.setting-item {
  margin-bottom: 16px;
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-group input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.btn-small {
  padding: 6px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.btn-small:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.threshold-controls, .feature-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.threshold-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.threshold-item label {
  font-size: 12px;
  color: #666;
}

.threshold-item input {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  cursor: pointer;
}

.current-status {
  background: #f0f8ff;
  padding: 12px;
  border-radius: 6px;
  margin: 16px 0;
}

.current-status h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #333;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.status-item label {
  color: #666;
}

.status-optimized { color: #28a745; font-weight: 600; }
.status-normal { color: #6c757d; }

.complexity-low { color: #28a745; }
.complexity-medium { color: #ffc107; }
.complexity-high { color: #fd7e14; }
.complexity-extreme { color: #dc3545; }

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}
</style>
