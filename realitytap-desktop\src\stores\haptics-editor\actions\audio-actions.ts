import { invoke } from "@tauri-apps/api/core";
import type { FileEditorState } from "../types";
import type { AudioAmplitudeData } from "../index";
import { logger, LogModule } from "@/utils/logger/logger";
import {
  processAudioDataIntelligently,
  getOptimizationStats,
  type OptimizedAudioAmplitudeData
} from "@/components/editor/waveform/utils/audio-optimization";

/**
 * 音频相关Actions接口
 */
export interface AudioActions {
  loadAudioData: (projectDirPath: string, audioRelativePath: string, maxSamples?: number) => Promise<void>;
  setAudioData: (data: AudioAmplitudeData | null, options?: {
    enableOptimization?: boolean;
    forceOptimization?: boolean;
    targetSampleCount?: number;
  }) => void;
  clearAudioData: () => void;
  getAudioData: () => AudioAmplitudeData | null;
  hasAudioData: () => boolean;
  retryAudioLoad: (projectDirPath: string, audioRelativePath: string, maxSamples?: number) => Promise<void>;
  getAudioOptimizationStats: () => any;
}

/**
 * 创建音频相关的Actions
 */
export function createAudioActions(
  state: FileEditorState,
  setState: (updates: Partial<FileEditorState>, options?: { skipHistoryRecord?: boolean }) => void
): AudioActions {

  /**
   * 根据文件类型和预估时长计算最优采样点数
   * 使用自适应精度策略，处理从20ms到数小时的音频
   */
  const calculateOptimalSampleCount = (audioRelativePath: string, estimatedDurationMs?: number): number => {
    const fileExt = audioRelativePath.toLowerCase().split('.').pop() || '';

    // 如果有预估时长，使用自适应精度策略
    if (estimatedDurationMs) {
      // 时长阈值
      const VERY_SHORT_THRESHOLD = 1_000;      // 1秒
      const SHORT_THRESHOLD = 120_000;         // 2分钟
      const MEDIUM_THRESHOLD = 600_000;        // 10分钟

      // 时间分辨率目标（每个采样点代表的毫秒数）
      const SHORT_MS_PER_SAMPLE = 50.0;        // 1秒-2分钟音频
      const MEDIUM_MS_PER_SAMPLE = 100.0;      // 2分钟-10分钟音频
      const LONG_MS_PER_SAMPLE = 200.0;        // >10分钟音频

      // 绝对限制
      const MIN_SAMPLES = 64;     // 极短音频最小值
      const MAX_SAMPLES = 8192;   // 控制性能的最大值

      let calculatedSamples: number;

      if (estimatedDurationMs <= VERY_SHORT_THRESHOLD) {
        // 极短音频：固定范围线性插值（64-256个点）
        const ratio = estimatedDurationMs / VERY_SHORT_THRESHOLD;
        calculatedSamples = Math.floor(64 + ratio * (256 - 64));
      } else if (estimatedDurationMs <= SHORT_THRESHOLD) {
        // 短音频：50ms/点
        const baseSamples = 256; // 来自极短音频（1秒）
        const remainingDuration = estimatedDurationMs - VERY_SHORT_THRESHOLD;
        const additionalSamples = Math.floor(remainingDuration / SHORT_MS_PER_SAMPLE);
        calculatedSamples = baseSamples + additionalSamples;
      } else if (estimatedDurationMs <= MEDIUM_THRESHOLD) {
        // 中等音频：100ms/点
        const baseSamplesVeryShort = 256;
        const shortDuration = SHORT_THRESHOLD - VERY_SHORT_THRESHOLD;
        const baseSamplesShort = Math.floor(shortDuration / SHORT_MS_PER_SAMPLE);
        const remainingDuration = estimatedDurationMs - SHORT_THRESHOLD;
        const additionalSamples = Math.floor(remainingDuration / MEDIUM_MS_PER_SAMPLE);
        calculatedSamples = baseSamplesVeryShort + baseSamplesShort + additionalSamples;
      } else {
        // 长音频：200ms/点
        const baseSamplesVeryShort = 256;
        const shortDuration = SHORT_THRESHOLD - VERY_SHORT_THRESHOLD;
        const baseSamplesShort = Math.floor(shortDuration / SHORT_MS_PER_SAMPLE);
        const mediumDuration = MEDIUM_THRESHOLD - SHORT_THRESHOLD;
        const baseSamplesMedium = Math.floor(mediumDuration / MEDIUM_MS_PER_SAMPLE);
        const remainingDuration = estimatedDurationMs - MEDIUM_THRESHOLD;
        const additionalSamples = Math.floor(remainingDuration / LONG_MS_PER_SAMPLE);
        calculatedSamples = baseSamplesVeryShort + baseSamplesShort + baseSamplesMedium + additionalSamples;
      }

      // 应用限制
      return Math.max(MIN_SAMPLES, Math.min(MAX_SAMPLES, calculatedSamples));
    }

    // 基于文件类型的默认策略（当没有预估时长时）
    if (fileExt === 'mp4') {
      // 视频文件通常较长，使用中等采样点数
      return 2400; // 相当于约2分钟音频的采样点数
    } else {
      // 音频文件，使用标准采样点数
      return 1200; // 相当于约1分钟音频的采样点数
    }
  };

  /**
   * 异步加载音频振幅数据
   */
  const loadAudioData = async (projectDirPath: string, audioRelativePath: string, maxSamples?: number): Promise<void> => {
    if (!projectDirPath || !audioRelativePath) {
      setState({ audioAmplitudeData: null });
      return;
    }

    try {
      logger.debug(LogModule.WAVEFORM, "开始加载音频振幅数据", {
        projectDirPath,
        audioRelativePath,
        maxSamples
      });

      // 如果没有指定maxSamples，使用智能计算
      const effectiveMaxSamples = maxSamples || calculateOptimalSampleCount(audioRelativePath);

      const amplitudeData = await invoke<AudioAmplitudeData>("get_audio_amplitude_data", {
        projectDirPath,
        audioRelativePath,
        maxSamples: effectiveMaxSamples,
      });

      setState({
        audioAmplitudeData: amplitudeData,
        audioDuration: amplitudeData.duration_ms
      });

      logger.info(LogModule.WAVEFORM, "音频振幅数据加载成功", {
        samplesCount: amplitudeData.samples.length,
        duration: amplitudeData.duration_ms,
        sampleRate: amplitudeData.sample_rate,
        requestedMaxSamples: effectiveMaxSamples,
        fileType: audioRelativePath.toLowerCase().split('.').pop(),
      });
    } catch (error) {
      logger.error(LogModule.WAVEFORM, "加载音频振幅数据失败", error);

      setState({
        audioAmplitudeData: null,
        audioDuration: null
      });

      throw error;
    }
  };

  /**
   * 直接设置音频数据（现在支持智能优化）
   */
  const setAudioData = (data: AudioAmplitudeData | null, options: {
    enableOptimization?: boolean;
    forceOptimization?: boolean;
    targetSampleCount?: number;
  } = {}) => {
    if (data) {
      // 验证数据有效性
      if (typeof data === "object" && data.samples && Array.isArray(data.samples) && data.samples.length > 0) {
        const { enableOptimization = true, forceOptimization = false, targetSampleCount } = options;

        logger.debug(LogModule.WAVEFORM, "设置有效的音频数据", {
          samplesCount: data.samples.length,
          duration: data.duration_ms,
          sampleRate: data.sample_rate,
          enableOptimization,
          forceOptimization
        });

        // 智能优化处理
        let processedData = data;
        if (enableOptimization) {
          try {
            processedData = processAudioDataIntelligently(data, {
              forceOptimization,
              targetSampleCount,
              enableLogging: true
            });

            // 记录优化统计信息
            const stats = getOptimizationStats(processedData);
            if (stats.isOptimized) {
              logger.info(LogModule.WAVEFORM, "Store音频数据已优化", {
                originalCount: stats.originalCount,
                optimizedCount: stats.currentCount,
                optimizationRatio: (stats.optimizationRatio! * 100).toFixed(1) + '%',
                complexityLevel: stats.complexityLevel
              });
            }
          } catch (error) {
            logger.error(LogModule.WAVEFORM, "Store音频数据优化失败，使用原始数据", error);
            processedData = data;
          }
        }

        setState({
          audioAmplitudeData: processedData,
          audioDuration: processedData.duration_ms
        });
      } else {
        logger.warn(LogModule.WAVEFORM, "接收到无效的音频数据，清理状态");
        setState({
          audioAmplitudeData: null,
          audioDuration: null
        });
      }
    } else {
      logger.debug(LogModule.WAVEFORM, "设置音频数据为null，清理状态");
      setState({
        audioAmplitudeData: null,
        audioDuration: null
      });
    }
  };

  /**
   * 清除音频数据
   */
  const clearAudioData = () => {
    logger.debug(LogModule.WAVEFORM, "清除音频数据");
    setState({
      audioAmplitudeData: null,
      audioDuration: null
    });
  };

  /**
   * 获取音频数据
   */
  const getAudioData = (): AudioAmplitudeData | null => {
    return state.audioAmplitudeData;
  };

  /**
   * 检查是否有音频数据
   */
  const hasAudioData = (): boolean => {
    return !!(state.audioAmplitudeData &&
              state.audioAmplitudeData.samples &&
              Array.isArray(state.audioAmplitudeData.samples) &&
              state.audioAmplitudeData.samples.length > 0);
  };

  /**
   * 重试音频数据加载
   */
  const retryAudioLoad = async (projectDirPath: string, audioRelativePath: string, maxSamples?: number): Promise<void> => {
    logger.info(LogModule.WAVEFORM, "重试加载音频数据", { projectDirPath, audioRelativePath });
    return loadAudioData(projectDirPath, audioRelativePath, maxSamples);
  };

  /**
   * 获取当前音频数据的优化统计信息
   */
  const getAudioOptimizationStats = () => {
    const audioData = getAudioData();
    if (!audioData) return null;
    return getOptimizationStats(audioData);
  };

  return {
    loadAudioData,
    setAudioData,
    clearAudioData,
    getAudioData,
    hasAudioData,
    retryAudioLoad,
    getAudioOptimizationStats,
  };
}
