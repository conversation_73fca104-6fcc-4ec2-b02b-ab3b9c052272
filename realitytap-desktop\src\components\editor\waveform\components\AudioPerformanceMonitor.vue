<template>
  <div v-if="showMonitor" class="audio-performance-monitor">
    <div class="monitor-header">
      <h4>音频性能监控</h4>
      <div class="monitor-controls">
        <button @click="toggleAutoRefresh" :class="['btn-small', { active: autoRefresh }]">
          {{ autoRefresh ? '停止' : '开始' }}监控
        </button>
        <button @click="clearStats" class="btn-small">清除</button>
        <button @click="toggleExpanded" class="btn-small">
          {{ isExpanded ? '收起' : '详情' }}
        </button>
      </div>
    </div>

    <div class="monitor-content">
      <!-- 核心性能指标 -->
      <div class="performance-summary">
        <div class="metric-card" :class="getRenderTimeClass(currentMetrics.renderTime)">
          <div class="metric-label">渲染时间</div>
          <div class="metric-value">{{ currentMetrics.renderTime.toFixed(1) }}ms</div>
          <div class="metric-target">目标: &lt;16ms</div>
        </div>
        
        <div class="metric-card" :class="getFpsClass(currentMetrics.fps)">
          <div class="metric-label">帧率</div>
          <div class="metric-value">{{ currentMetrics.fps.toFixed(0) }}</div>
          <div class="metric-target">目标: ≥30fps</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-label">内存使用</div>
          <div class="metric-value">{{ (currentMetrics.memoryUsage / 1024 / 1024).toFixed(1) }}MB</div>
          <div class="metric-target">{{ memoryTrend }}</div>
        </div>
        
        <div class="metric-card" :class="getOptimizationClass(optimizationStats?.isOptimized)">
          <div class="metric-label">优化状态</div>
          <div class="metric-value">{{ optimizationStats?.isOptimized ? '已优化' : '未优化' }}</div>
          <div class="metric-target">
            {{ optimizationStats?.complexityLevel ? getComplexityText(optimizationStats.complexityLevel) : 'N/A' }}
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div v-if="isExpanded" class="detailed-stats">
        <!-- 优化统计 -->
        <div v-if="optimizationStats" class="stats-section">
          <h5>优化统计</h5>
          <div class="stats-grid">
            <div class="stat-item">
              <label>原始采样点:</label>
              <span>{{ optimizationStats.originalCount || 'N/A' }}</span>
            </div>
            <div class="stat-item">
              <label>当前采样点:</label>
              <span>{{ optimizationStats.currentCount || 'N/A' }}</span>
            </div>
            <div class="stat-item">
              <label>优化比例:</label>
              <span>{{ getOptimizationRatio() }}%</span>
            </div>
            <div class="stat-item">
              <label>复杂度分数:</label>
              <span>{{ optimizationStats.complexityScore?.toFixed(0) || 'N/A' }}</span>
            </div>
            <div class="stat-item">
              <label>保留特征:</label>
              <span>{{ optimizationStats.preservedFeatures || 0 }}</span>
            </div>
            <div class="stat-item">
              <label>内存节省:</label>
              <span>{{ getMemorySaving() }}%</span>
            </div>
          </div>
        </div>

        <!-- 性能历史 -->
        <div class="stats-section">
          <h5>性能历史</h5>
          <div class="performance-history">
            <div class="history-chart">
              <canvas ref="chartCanvas" width="300" height="100"></canvas>
            </div>
            <div class="history-stats">
              <div class="history-item">
                <label>平均渲染时间:</label>
                <span>{{ getAverageRenderTime().toFixed(1) }}ms</span>
              </div>
              <div class="history-item">
                <label>最大渲染时间:</label>
                <span>{{ getMaxRenderTime().toFixed(1) }}ms</span>
              </div>
              <div class="history-item">
                <label>平均帧率:</label>
                <span>{{ getAverageFps().toFixed(0) }}fps</span>
              </div>
              <div class="history-item">
                <label>最低帧率:</label>
                <span>{{ getMinFps().toFixed(0) }}fps</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 建议 -->
        <div class="stats-section">
          <h5>性能建议</h5>
          <div class="recommendations">
            <div v-for="recommendation in getRecommendations()" :key="recommendation.type" 
                 :class="['recommendation', `recommendation-${recommendation.type}`]">
              <div class="recommendation-icon">{{ recommendation.icon }}</div>
              <div class="recommendation-text">{{ recommendation.text }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';

// Props
interface Props {
  showMonitor?: boolean;
  optimizationStats?: {
    originalCount?: number;
    currentCount?: number;
    isOptimized?: boolean;
    complexityLevel?: 'low' | 'medium' | 'high' | 'extreme';
    complexityScore?: number;
    preservedFeatures?: number;
    optimizationRatio?: number;
  };
  refreshInterval?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showMonitor: true,
  refreshInterval: 1000
});

// 状态
const isExpanded = ref(false);
const autoRefresh = ref(false);
const chartCanvas = ref<HTMLCanvasElement | null>(null);

// 性能指标
const currentMetrics = ref({
  renderTime: 0,
  fps: 0,
  memoryUsage: 0,
  timestamp: Date.now()
});

// 历史数据
const performanceHistory = ref<Array<{
  renderTime: number;
  fps: number;
  memoryUsage: number;
  timestamp: number;
}>>([]);

// 定时器
let refreshTimer: number | null = null;
let lastFrameTime = performance.now();
let frameCount = 0;

// 计算属性
const memoryTrend = computed(() => {
  if (performanceHistory.value.length < 2) return 'N/A';
  const recent = performanceHistory.value.slice(-5);
  const trend = recent[recent.length - 1].memoryUsage - recent[0].memoryUsage;
  return trend > 0 ? '↗️' : trend < 0 ? '↘️' : '→';
});

// 方法
const getRenderTimeClass = (renderTime: number) => {
  if (renderTime < 16) return 'metric-good';
  if (renderTime < 33) return 'metric-warning';
  return 'metric-bad';
};

const getFpsClass = (fps: number) => {
  if (fps >= 30) return 'metric-good';
  if (fps >= 20) return 'metric-warning';
  return 'metric-bad';
};

const getOptimizationClass = (isOptimized?: boolean) => {
  return isOptimized ? 'metric-good' : 'metric-neutral';
};

const getComplexityText = (level: string) => {
  const levelMap = {
    low: '低复杂度',
    medium: '中等复杂度',
    high: '高复杂度',
    extreme: '极高复杂度'
  };
  return levelMap[level as keyof typeof levelMap] || level;
};

const getOptimizationRatio = () => {
  if (!props.optimizationStats?.originalCount || !props.optimizationStats?.currentCount) return 0;
  return ((props.optimizationStats.currentCount / props.optimizationStats.originalCount) * 100).toFixed(1);
};

const getMemorySaving = () => {
  if (!props.optimizationStats?.originalCount || !props.optimizationStats?.currentCount) return 0;
  const originalSize = props.optimizationStats.originalCount * 8; // 假设每个采样点8字节
  const currentSize = props.optimizationStats.currentCount * 8;
  return (((originalSize - currentSize) / originalSize) * 100).toFixed(1);
};

const getAverageRenderTime = () => {
  if (performanceHistory.value.length === 0) return 0;
  const sum = performanceHistory.value.reduce((acc, item) => acc + item.renderTime, 0);
  return sum / performanceHistory.value.length;
};

const getMaxRenderTime = () => {
  if (performanceHistory.value.length === 0) return 0;
  return Math.max(...performanceHistory.value.map(item => item.renderTime));
};

const getAverageFps = () => {
  if (performanceHistory.value.length === 0) return 0;
  const sum = performanceHistory.value.reduce((acc, item) => acc + item.fps, 0);
  return sum / performanceHistory.value.length;
};

const getMinFps = () => {
  if (performanceHistory.value.length === 0) return 0;
  return Math.min(...performanceHistory.value.map(item => item.fps));
};

const getRecommendations = () => {
  const recommendations = [];
  
  if (currentMetrics.value.renderTime > 33) {
    recommendations.push({
      type: 'error',
      icon: '⚠️',
      text: '渲染时间过长，建议启用音频优化或降低性能目标'
    });
  } else if (currentMetrics.value.renderTime > 16) {
    recommendations.push({
      type: 'warning',
      icon: '💡',
      text: '渲染时间偏高，可考虑启用性能优先模式'
    });
  }
  
  if (currentMetrics.value.fps < 20) {
    recommendations.push({
      type: 'error',
      icon: '🐌',
      text: '帧率过低，强烈建议启用音频优化'
    });
  } else if (currentMetrics.value.fps < 30) {
    recommendations.push({
      type: 'warning',
      icon: '📈',
      text: '帧率偏低，建议调整优化设置'
    });
  }
  
  if (!props.optimizationStats?.isOptimized && props.optimizationStats?.complexityLevel === 'high') {
    recommendations.push({
      type: 'info',
      icon: '🚀',
      text: '检测到高复杂度音频，建议启用自动优化'
    });
  }
  
  if (recommendations.length === 0) {
    recommendations.push({
      type: 'success',
      icon: '✅',
      text: '性能表现良好，无需调整'
    });
  }
  
  return recommendations;
};

const updateMetrics = () => {
  const now = performance.now();
  frameCount++;
  
  // 计算FPS
  if (now - lastFrameTime >= 1000) {
    currentMetrics.value.fps = frameCount;
    frameCount = 0;
    lastFrameTime = now;
  }
  
  // 模拟渲染时间测量
  const renderStart = performance.now();
  // 这里应该是实际的渲染逻辑
  const renderEnd = performance.now();
  currentMetrics.value.renderTime = renderEnd - renderStart;
  
  // 获取内存使用情况
  if ('memory' in performance) {
    currentMetrics.value.memoryUsage = (performance as any).memory.usedJSHeapSize;
  }
  
  currentMetrics.value.timestamp = now;
  
  // 添加到历史记录
  performanceHistory.value.push({ ...currentMetrics.value });
  
  // 保持历史记录在合理范围内
  if (performanceHistory.value.length > 60) {
    performanceHistory.value = performanceHistory.value.slice(-60);
  }
  
  // 更新图表
  nextTick(() => {
    updateChart();
  });
};

const updateChart = () => {
  if (!chartCanvas.value) return;
  
  const ctx = chartCanvas.value.getContext('2d');
  if (!ctx) return;
  
  const width = chartCanvas.value.width;
  const height = chartCanvas.value.height;
  
  // 清除画布
  ctx.clearRect(0, 0, width, height);
  
  if (performanceHistory.value.length < 2) return;
  
  // 绘制渲染时间曲线
  ctx.strokeStyle = '#007bff';
  ctx.lineWidth = 2;
  ctx.beginPath();
  
  const maxRenderTime = Math.max(...performanceHistory.value.map(item => item.renderTime));
  const minRenderTime = Math.min(...performanceHistory.value.map(item => item.renderTime));
  const range = maxRenderTime - minRenderTime || 1;
  
  performanceHistory.value.forEach((item, index) => {
    const x = (index / (performanceHistory.value.length - 1)) * width;
    const y = height - ((item.renderTime - minRenderTime) / range) * height;
    
    if (index === 0) {
      ctx.moveTo(x, y);
    } else {
      ctx.lineTo(x, y);
    }
  });
  
  ctx.stroke();
  
  // 绘制目标线（16ms）
  if (maxRenderTime > 16) {
    const targetY = height - ((16 - minRenderTime) / range) * height;
    ctx.strokeStyle = '#28a745';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(0, targetY);
    ctx.lineTo(width, targetY);
    ctx.stroke();
    ctx.setLineDash([]);
  }
};

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value;
  
  if (autoRefresh.value) {
    refreshTimer = window.setInterval(updateMetrics, props.refreshInterval);
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  }
};

const clearStats = () => {
  performanceHistory.value = [];
  currentMetrics.value = {
    renderTime: 0,
    fps: 0,
    memoryUsage: 0,
    timestamp: Date.now()
  };
  updateChart();
};

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

// 生命周期
onMounted(() => {
  // 自动开始监控
  if (props.showMonitor) {
    toggleAutoRefresh();
  }
});

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});

// 监听显示状态变化
watch(() => props.showMonitor, (show) => {
  if (show && !autoRefresh.value) {
    toggleAutoRefresh();
  } else if (!show && autoRefresh.value) {
    toggleAutoRefresh();
  }
});
</script>

<style scoped>
.audio-performance-monitor {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
}

.monitor-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.monitor-controls {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.btn-small:hover {
  background: #f8f9fa;
}

.btn-small.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.monitor-content {
  padding: 16px;
}

.performance-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.metric-card {
  padding: 12px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e0e0e0;
}

.metric-good {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border-color: #28a745;
}

.metric-warning {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-color: #ffc107;
}

.metric-bad {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  border-color: #dc3545;
}

.metric-neutral {
  background: #f8f9fa;
  border-color: #6c757d;
}

.metric-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.metric-target {
  font-size: 10px;
  color: #888;
}

.detailed-stats {
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-section h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 4px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 4px 0;
}

.stat-item label {
  color: #666;
}

.stat-item span {
  font-weight: 500;
  color: #333;
}

.performance-history {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.history-chart {
  flex: 1;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
}

.history-stats {
  flex: 0 0 150px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  margin-bottom: 4px;
}

.history-item label {
  color: #666;
}

.history-item span {
  font-weight: 500;
  color: #333;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recommendation {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
}

.recommendation-success {
  background: #d4edda;
  color: #155724;
}

.recommendation-info {
  background: #d1ecf1;
  color: #0c5460;
}

.recommendation-warning {
  background: #fff3cd;
  color: #856404;
}

.recommendation-error {
  background: #f8d7da;
  color: #721c24;
}

.recommendation-icon {
  font-size: 14px;
}

.recommendation-text {
  flex: 1;
}
</style>
